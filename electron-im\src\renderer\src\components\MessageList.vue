<!-- 消息列表组件 -->
<template>
  <div ref="messagesArea" class="flex-1 overflow-y-auto p-4 custom-scrollbar">
    <!-- 未选择联系人时的占位符 -->
    <div
      v-if="!currentContact"
      class="flex flex-col items-center justify-center h-full text-gray-500"
    >
      <div class="w-40 h-40 mb-6">
        <img src="../assets/editor/chat-placeholder.svg" alt="选择聊天" class="w-full h-full" />
      </div>
      <p class="text-sm font-medium">请选择一个聊天开始对话</p>
    </div>
    <!-- 已选择联系人但无消息 -->
    <div
      v-else-if="!messages.length"
      class="flex items-center justify-center h-full text-gray-500 text-sm"
    >
      暂无消息
    </div>
    <div v-else>
      <div v-for="message in messages" :key="message.id" class="mb-4">
        <!-- 判断是否为当前用户发送的消息 -->
        <div v-if="isCurrentUserMessage(message)" class="flex items-start gap-3 justify-end">
          <!-- 当前用户消息：内容在左，头像在右 -->
          <div class="flex-1 flex flex-col">
            <div class="flex items-center gap-2 mb-2 text-right">
              <span class="text-xs text-gray-400 font-normal">
                {{ formatMessageTime(message.timestamp) }}
              </span>
              <span class="text-xs text-gray-600 font-medium">{{ message.senderName }}</span>
            </div>
            <div class="bg-[#c9daf5] text-white rounded-lg p-3 max-w-md shadow-sm">
              <div
                class="text-sm leading-relaxed text-gray-900 break-words whitespace-pre-wrap"
                v-text="message.content"
              ></div>
            </div>
          </div>
          <UserDetailPopover
            :user-id="getCurrentUserId(message)"
            v-model:open="popoverStates[message.id + '_current']"
          >
            <UserAvatar
              :name="message.senderName"
              size="medium"
              class="cursor-pointer hover:opacity-80 transition-opacity"
            />
          </UserDetailPopover>
        </div>

        <!-- 其他用户消息：头像在左，内容在右 -->
        <div v-else class="flex items-start gap-3">
          <UserDetailPopover :user-id="message.senderId" v-model:open="popoverStates[message.id]">
            <UserAvatar
              :name="message.senderName"
              size="medium"
              class="cursor-pointer hover:opacity-80 transition-opacity"
            />
          </UserDetailPopover>
          <div class="flex flex-col">
            <div class="flex items-center gap-2 mb-2">
              <span class="text-xs text-gray-600 font-medium">{{ message.senderName }}</span>
              <span class="text-xs text-gray-400 font-normal">
                {{ formatMessageTime(message.timestamp) }}
              </span>
            </div>
            <div class="bg-[#ffffff] border border-gray-200 rounded-lg p-3 shadow-sm max-w-md">
              <div
                class="text-sm text-gray-900 leading-relaxed break-words whitespace-pre-wrap"
                v-text="message.content"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, reactive, onMounted, onUnmounted } from 'vue'
import UserAvatar from './UserAvatar.vue'
import UserDetailPopover from './UserDetailPopover.vue'
import { useUserStore } from '../store/user'

import type { User } from '../api'

interface Message {
  id: string
  senderId: string
  senderName: string
  content: string
  timestamp: Date
}

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  user: User
  hasLastMessage?: boolean
}

interface Props {
  messages: Message[]
  currentContact: Contact | null
}

const props = defineProps<Props>()

// 用户状态管理
const userStore = useUserStore()

const messagesArea = ref<HTMLElement>()

// 管理每个消息的 Popover 状态
const popoverStates = reactive<Record<string, boolean>>({})

// 判断是否为当前用户发送的消息
const isCurrentUserMessage = (message: Message) => {
  const currentUserId = userStore.currentUser.value?.id
  return message.senderId === currentUserId
}

// 获取当前用户的真实 ID
const getCurrentUserId = (message: Message) => {
  if (isCurrentUserMessage(message)) {
    return userStore.currentUser?.id || null
  }
  return message.senderId
}

// 初始化 Popover 状态
watch(
  () => props.messages,
  (newMessages) => {
    newMessages.forEach((message) => {
      // 为其他用户消息创建 Popover 状态
      if (!(message.id in popoverStates)) {
        popoverStates[message.id] = false
      }
      // 为当前用户消息创建 Popover 状态
      const currentUserKey = message.id + '_current'
      if (!(currentUserKey in popoverStates)) {
        popoverStates[currentUserKey] = false
      }
    })
  },
  { immediate: true, deep: true }
)

// 滚动到底部
const scrollToBottom = () => {
  if (messagesArea.value) {
    messagesArea.value.scrollTop = messagesArea.value.scrollHeight
  }
}

// 监听消息变化，自动滚动到底部
watch(
  () => props.messages,
  () => {
    nextTick(() => {
      scrollToBottom()
    })
  },
  { deep: true }
)

// 添加 ResizeObserver 来监听容器高度变化
let resizeObserver: ResizeObserver | null = null

onMounted(() => {
  // 监听容器高度变化
  if (messagesArea.value) {
    resizeObserver = new ResizeObserver(() => {
      nextTick(() => {
        scrollToBottom()
      })
    })
    resizeObserver.observe(messagesArea.value)
  }
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

// 时间格式化函数
const formatMessageTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 暴露滚动方法给父组件
defineExpose({
  scrollToBottom
})
</script>

<style scoped>
/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道背景色 */
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1; /* 滚动条滑块背景色 */
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8; /* 滚动条滑块悬停背景色 */
}
</style>
